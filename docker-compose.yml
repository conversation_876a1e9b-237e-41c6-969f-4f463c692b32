version: '3.8'

services:
  # 主应用服务
  wise-match-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: wise-match-app-dev
    ports:
      - "8000:8000"
    environment:
      - WISE_MATCH_ENV=dev
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=wise_match_user
      - DB_PASSWORD=wise_match_password
      - DB_NAME=wise_match_db
      - LOG_LEVEL=DEBUG
      - MAX_WORKERS=5
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - wise-match-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: wise-match-mysql-dev
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: wise_match_db
      MYSQL_USER: wise_match_user
      MYSQL_PASSWORD: wise_match_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - wise-match-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200

  # Redis缓存服务 (可选)
  redis:
    image: redis:7-alpine
    container_name: wise-match-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wise-match-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    command: redis-server --appendonly yes

  # Nginx反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: wise-match-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - wise-match-app
    networks:
      - wise-match-network
    restart: unless-stopped
    profiles:
      - production

  # 监控服务 - Prometheus (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: wise-match-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - wise-match-network
    restart: unless-stopped
    profiles:
      - monitoring
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # 监控服务 - Grafana (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: wise-match-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - wise-match-network
    restart: unless-stopped
    profiles:
      - monitoring
    depends_on:
      - prometheus

networks:
  wise-match-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
